<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDynamicGiftDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dynamic_gift_data', function (Blueprint $table) {
            $table->id();
            $table->string('txn_id', 255);
            $table->text('response');
            $table->string('txn_status', 255);
            $table->unsignedInteger('user_id')->nullable();
            $table->unsignedInteger('partner_id')->nullable();
            $table->double('amount')->nullable();
            $table->timestamps(); // Use 0 to disable the created_at and updated_at columns.
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dynamic_gift_data');
    }
}
